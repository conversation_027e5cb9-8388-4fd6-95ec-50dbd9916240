import fs from 'fs/promises';
import fsSync from 'fs';
import crypto from 'crypto';
import path from 'path';
import mime from 'mime-types';
import { Logger } from '@/util/log';

export class FileSystemHelper {
  private readonly logger: Logger = new Logger('FileSystemHelper');
  private readonly textExtensions = new Set([
    // 编程语言
    '.ts', '.js', '.jsx', '.tsx', '.py', '.rb', '.php', '.java',
    '.c', '.cpp', '.h', '.hpp', '.cs', '.go', '.rs', '.swift',
    '.kt', '.scala', '.r', '.m', '.lua', '.pl', '.sh', '.bash',
    '.ps1', '.psm1',

    // Web 相关
    '.html', '.htm', '.css', '.scss', '.sass', '.less', '.vue',
    '.svelte', '.wxml', '.wxss',

    // 配置文件
    '.json', '.xml', '.yaml', '.yml', '.toml', '.ini', '.conf',
    '.config', '.properties', '.prop', '.env', '.gradle', '.pom',
    '.maven', '.dockerfile', '.dockerignore', '.gitignore',
    '.npmrc', '.nvmrc',

    // 标记语言
    '.md', '.markdown', '.rst', '.tex', '.latex', '.wiki',

    // 数据文件
    '.sql', '.graphql', '.gql',

    // 其他文本文件
    '.txt', '.log', '.csv',
  ]);

  private readonly textMimeTypes = [
    'application/json',
    'application/javascript',
    'application/typescript',
    'application/xml',
    'application/x-yaml',
    'application/x-sh',
    'application/x-httpd-php',
    'application/x-python',
    'application/x-ruby',
    'application/x-perl',
  ];

  /**
   * 计算文件的 MD5 哈希值
   */
  public async getFileHash(fullPath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('md5');
      const stream = fsSync.createReadStream(fullPath);
      
      stream.on('data', (data: Buffer) => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', (error: Error) => reject(error));
    });
  }

  /**
   * 检查文件是否为文本文件
   */
  public async isTextFile(filePath: string): Promise<boolean> {
    try {
      const ext = path.extname(filePath).toLowerCase();
      
      if (this.textExtensions.has(ext)) {
        return true;
      }

      const mimeType = mime.lookup(filePath);
      if (!mimeType) return false;

      if (mimeType.startsWith('text/')) return true;

      return this.textMimeTypes.includes(mimeType);
    } catch (error) {
      this.logger.error(`Error checking file type: ${filePath}`, error);
      return true; // 发生错误时默认当做文本文件
    }
  }

  /**
   * 检查文件大小
   */
  public async getFileSize(filePath: string): Promise<number> {
    const stats = await fs.stat(filePath);
    return stats.size;
  }
} 