import mime from 'mime-types';
import path from 'path';
import fs from 'fs';
import { Logger } from '@/util/log';

const logger: Logger = new Logger('IndexManager-Utils');
export const IGNORED_PATTERNS = [
  /node_modules/,
  /\.git/,
  /\.idea/,
  /\.vscode/,
  /\.DS_Store/,
  /\.env/,
  /dist/,
  /build/,
  /\.log$/,
  /\.lock$/,
  /package-lock\.json$/,
  /yarn\.lock$/,
  /\.map$/,
  /\.min\.(js|css)$/,
  /\.(jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot|mp3|mp4|avi|mkv|zip|tar|gz|rar|7z|exe|dll|so|dylib|vsix|class)$/i,
  /\.svg$/i,
  // 编译后的文件
  /\.d\.ts$/, // TypeScript 声明文件
  /\.d\.mts$/, // TypeScript 模块声明文件
  /\.d\.cts$/, // TypeScript CommonJS 声明文件
  /\.js\.flow$/, // Flow 类型定义文件
  /\.(es|umd|amd|cjs|esm|iife|system|commonjs)\.(js|ts|jsx|tsx)$/i, // 各种模块格式的编译输出
  /\.(prod|dev|production|development|test|testing)\.(js|ts|jsx|tsx)$/i, // 环境特定的编译输出
  /\.(bundle|bundled|compiled|min|minified)\.(js|ts|jsx|tsx)$/i, // 打包或压缩后的文件
  /\.(browser|node|modern|legacy|compat)\.(js|ts|jsx|tsx)$/i // 目标环境特定的编译输出
];

// 添加已知的文本文件扩展名集合
export const TEXT_FILE_EXTENSIONS = new Set([
  // 配置文件
  '.mts',
  '.cts',
  '.cjs',
  '.mjs',
  '.json',
  '.xml',
  '.yaml',
  '.yml',
  '.toml',
  '.ini',
  '.conf',
  '.config',
  '.properties',
  '.prop',
  '.env',
  '.gradle',
  '.pom',
  '.maven',
  '.dockerfile',
  '.dockerignore',
  '.gitignore',
  '.npmrc',
  '.nvmrc',
  '.eslintrc',
  '.prettierrc',
  '.babelrc',
  '.tsconfig',
  '.editorconfig',

  // 编程语言
  '.ts',
  '.js',
  '.jsx',
  '.tsx',
  '.py',
  '.rb',
  '.php',
  '.java',
  '.c',
  '.cpp',
  '.h',
  '.hpp',
  '.cs',
  '.go',
  '.rs',
  '.swift',
  '.kt',
  '.scala',
  '.r',
  '.m',
  '.lua',
  '.pl',
  '.sh',
  '.bash',
  '.ps1',
  '.psm1',

  // Web 相关
  '.html',
  '.htm',
  '.css',
  '.scss',
  '.sass',
  '.less',
  '.vue',
  '.svelte',
  '.wxml',
  '.wxss',

  // 标记语言
  '.md',
  '.markdown',
  '.rst',
  '.tex',
  '.latex',
  '.wiki',

  // 数据文件
  '.sql',
  '.graphql',
  '.gql',
  '.csv',
  '.tsv',

  // 其他文本文件
  '.txt',
  '.log'
]);

export const shouldIgnoreFile = (filePath: string): boolean => {
  const shouldIgnore = IGNORED_PATTERNS.some((pattern) => pattern.test(filePath));
  if (shouldIgnore) {
    logger.debug(`File ${filePath} matched ignore pattern`);
  }
  return shouldIgnore;
};

export const isTextFile = async (filePath: string, dirPath: string): Promise<boolean> => {
  try {
    // 首先检查是否是被忽略的文件类型
    if (shouldIgnoreFile(filePath)) {
      logger.debug(`File ${filePath} is in ignored patterns`);
      return false;
    }

    // 检查文件扩展名
    const ext = path.extname(filePath).toLowerCase();

    // 特别检查SVG文件
    if (ext === '.svg') {
      logger.debug(`File ${filePath} is a SVG file, skipping`);
      return false;
    }

    // 如果在已知的文本文件扩展名列表中，直接返回 true
    if (TEXT_FILE_EXTENSIONS.has(ext)) {
      return true;
    }

    // 获取MIME类型
    const mimeType = mime.lookup(filePath);
    logger.debug(`File ${filePath} mime type: ${mimeType}`);

    // 如果是SVG mime类型，返回false
    if (mimeType === 'image/svg+xml') {
      logger.debug(`File ${filePath} is SVG by mime type, skipping`);
      return false;
    }

    // 如果没有扩展名，尝试读取文件的前几个字节来判断是否是二进制文件
    if (!ext) {
      try {
        const fullPath = path.join(dirPath, filePath);
        const buffer = await fs.promises.readFile(fullPath, { encoding: null, flag: 'r' });

        // 检查前 1024 个字节中是否包含空字节（二进制文件的特征）
        for (let i = 0; i < Math.min(buffer.length, 1024); i++) {
          if (buffer[i] === 0) {
            logger.debug(`File ${filePath} appears to be binary (contains null bytes)`);
            return false;
          }
        }
        return true;
      } catch (error) {
        logger.warn(`Error reading file ${filePath} for binary check:`, error);
        return false;
      }
    }

    // 如果扩展名不在列表中，使用 mime 类型判断
    if (!mimeType) {
      logger.debug(`No mime type found for ${filePath}, treating as non-text file`);
      return false;
    }

    // 检查 mime 类型
    const isText =
      mimeType.startsWith('text/') ||
      mimeType.includes('javascript') ||
      mimeType.includes('json') ||
      mimeType.includes('xml') ||
      mimeType.includes('yaml') ||
      mimeType.includes('typescript');

    if (!isText) {
      logger.debug(`File ${filePath} has non-text mime type: ${mimeType}`);
    }

    return isText;
  } catch (error) {
    logger.error(`Error in isTextFile for ${filePath}:`, error);
    return false;
  }
};
