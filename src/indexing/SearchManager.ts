import { NonCodeEmbedding } from '@/embedding/NoneCodeEmbedding';
import { LanceDB } from '@/db/lancedb';
import { CodeEmbedding } from '@/embedding/CodeEmbedding';
import { Logger } from '@/util/log';
import { AGENT_NAMESPACE, generateCollectionName } from '@/util/const';
import { Api } from '@/http';
import { IdeCommonMessage } from '@/protocol/messenger';
import { GlobalConfig } from '@/util/global';
export class SearchManager {
  private collectionName: string;
  private codeDbEmbedding?: CodeEmbedding;
  private nonCodeDbEmbedding?: NonCodeEmbedding;
  private logger = new Logger('SearchManager');
  private httpClient = new Api();

  constructor(collectionName: string) {
    this.collectionName = collectionName;
    this.codeDbEmbedding = new CodeEmbedding(collectionName);
    this.nonCodeDbEmbedding = new NonCodeEmbedding(collectionName);
  }

  async search(query: string, topK: number, targetDirectory: string[], chatHistory: any, enable_rewrite = false) {
    const startTime = Date.now();
    const body = {
      query,
      chat_history: chatHistory,
      enable_rewrite
    };
    this.logger.debug(`queryEmbedding body, ${JSON.stringify(body)}`);
    const data = await this.httpClient.queryEmbedding(body);
    this.logger.info(`queryEmbedding result, ${JSON.stringify(data)}`);
    const { code_embedding, nl_embedding, rerank_query, sparse_embedding } = data;
    // search database

    const searchResult = await this.retrieveData(topK, targetDirectory, code_embedding, nl_embedding, sparse_embedding);
    this.logger.info(`searchResult, ${JSON.stringify(searchResult)}`);
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'search/retrieveDataSize',
      millis: Array.isArray(searchResult) ? searchResult.length : 0,
      extra3: Array.isArray(searchResult) ? String(searchResult.length) : '',
      extra4: targetDirectory.join(','),
      extra6: query
    });
    const rerankBody = {
      query: rerank_query,
      code_context_list: searchResult,
      top_k: topK
    };
    this.logger.debug(`rerankBody, ${JSON.stringify(rerankBody)}`);
    const rerankResult = await this.httpClient.codeRerank(rerankBody);
    this.logger.info(`rerankResult, ${JSON.stringify(rerankResult)}`);

    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'search/rerankSize',
      millis: Array.isArray(rerankResult?.code_context_list) ? rerankResult.code_context_list.length : 0,
      extra3: Array.isArray(rerankResult?.code_context_list) ? String(rerankResult.code_context_list.length) : '',
      extra4: targetDirectory.join(','),
      extra6: query
    });
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'search/search',
      millis: Date.now() - startTime,
      extra3: GlobalConfig.getConfig().getPlatform(),
      extra6: 'success'
    });
    return rerankResult;
  }
  async retrieveData(
    topK: number,
    targetDirectory: string[],
    CodeVector: number[] = [],
    NlVector: number[] = [],
    SparseVector: Record<string, number> = {},
    isCodeData = true
  ) {
    const outputFields = [
      'node_id',
      'snippet',
      'language',
      'sub_id',
      'is_sub_node',
      'code_type',
      'file_path',
      'name',
      'signature',
      'caller_references',
      'callee_references'
    ];

    const filterStr =
      targetDirectory.length > 0 ? targetDirectory.map((dir) => `file_path LIKE '%${dir}%'`).join(' OR ') : '';
    const initializer = isCodeData ? this.codeDbEmbedding : this.nonCodeDbEmbedding;
    const targetCollection = `${this.collectionName}`;

    if (!initializer) {
      throw new Error(`${isCodeData ? 'Code' : 'Non-code'} DB initializer is not initialized`);
    }

    const dataList: any[] = [];
    const startTime = Date.now();

    // Search based on code vector
    if (CodeVector.length > 0) {
      const table = await LanceDB.getCodeTable(targetCollection);
      let query = table.search(CodeVector, 'code_vector');

      if (filterStr) {
        query = query.where(filterStr);
      }
      query = query.limit(topK);
      const codeSearchRes = await query.select(outputFields);
      const list = await LanceDB.getTableData(codeSearchRes);
      this.logger.info(`codeSearchRes ${list.length}`);
      dataList.push(...list);
    }

    // Search based on nl vector
    if (NlVector.length > 0) {
      const table = await LanceDB.getCodeTable(targetCollection);
      let query = table.search(NlVector, 'nl_vector');

      if (filterStr) {
        query = query.where(filterStr);
      }
      query = query.limit(topK);
      const nlSearchRes = await query.select(outputFields);
      const list = await LanceDB.getTableData(nlSearchRes);
      this.logger.info(`nlSearchRes ${list.length}`);
      dataList.push(...list);
    }

    // Search based on sparse encoding
    if (SparseVector && Object.keys(SparseVector).length > 0) {
      // todo: 使用模糊搜索代替
      const table = await LanceDB.getCodeTable(targetCollection);
      const sparseSearchRes = await LanceDB.searchBySparseVector(table, SparseVector, outputFields, topK, filterStr);

      this.logger.info(`sparseSearchRes ${sparseSearchRes.length}`);

      dataList.push(...sparseSearchRes);
    }
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'retrieveDataCost',
      millis: Date.now() - startTime,
      extra3: topK.toString(),
      extra4: targetDirectory.join(',')
    });
    // Convert the final result to plain objects
    return dataList.map((item) => {
      return {
        node_id: item.node_id,
        sub_node_id: item.sub_id,
        is_sub_node: item.is_sub_node,
        code_content: item.snippet,
        metadata: {
          name: item.name,
          signature: item.signature,
          language: item.language,
          file_path: item.file_path,
          code_type: item.code_type,
          callers: item.caller_references,
          callees: item.callee_references
        }
      };
    });
  }
}
