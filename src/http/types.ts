export interface HttpClientResponse<T> {
  data: T;
  host: string;
  port: number;
  message: string;
  status: number;
  timestamp: string;
  traceId: string;
}

export interface QueryEmbeddingBody {
  chat_history: {
    content: string;
    role: string;
  }[];
  enable_rewrite: boolean;
  query: string;
}
export interface QueryEmbeddingResponse {
  code_embedding: number[];
  nl_embedding: number[];
  rerank_query: string;
  sparse_embedding: Record<string, number>;
}

export interface CodeRerankBody {
  code_context_list: {
    is_sub_node: boolean;
    code_content: string;
    metadata: {
      callees: string[];
      callers: string[];
      code_type: string;
      file_path: string;
      language: string;
      name: string;
      signature: string;
    };
    node_id: string;
    sub_node_id: number;
  }[];
  query: string;
  top_k: number;
}
export interface CodeRerankResponse {
  code_context_list: {
    _sub_node: boolean;
    code_content: string;
    metadata: {
      callees: string[];
      callers: string[];
      code_type: string;
      file_path: string;
      language: string;
      name: string;
      signature: string;
    };
    node_id: string;
    sub_node_id: number;
  }[];
}
