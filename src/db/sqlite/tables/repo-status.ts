import { SqliteDb } from '../index.js';

export interface RepoStatus {
  /** 自增主键 */
  id: number;
  /** 代码仓库地址 */
  git_url: string;
  /** 代码仓库路径(相对系统根目录的绝对路径) */
  dir_path: string;
  /** 当前分支 */
  branch?: string;
  /** 当前index的最后一次commitid */
  commit_id: string;
  /** 最后更新时间戳 */
  updated_at: number;
  /** 创建时间戳 */
  created_at: number;
  /** IDE版本 */
  ide_version: string;
  /** 插件版本 */
  plugin_version: string;
  /** 插件平台: xcode/vscode/jetbrains */
  platform: 'xcode' | 'vscode' | 'jetbrains';
  /** 总文件数 */
  total_files: number;
  /** 已完成文件数 */
  done_files: number;
  /** 是否正在构建 */
  is_building: boolean;
  /** 状态: 0: 成功, 1: 超过最大索引大小5k, 2: 超过最大索引大小10k, 9999: 索引失败, 11: 不是git仓库 */
  status: RepoStatusEnum;
  /** 错误信息 */
  message?: string;
}

export enum RepoStatusEnum {
  SUCCESS = '0',
  MAX_INDEX_SIZE_EXCEEDED_5K = '1',
  MAX_INDEX_SIZE_EXCEEDED_10K = '2',
  INDEX_FAILED = '9999',
  NOT_GIT_REPO = '11'
}

export const RepoStatusMessageMap = {
  [RepoStatusEnum.SUCCESS]: '成功',
  [RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_5K]: `索引失败，可索引文件数量超过最大索引数量5000`,
  [RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_10K]: `索引失败，可索引文件数量超过最大索引数量10000`,
  [RepoStatusEnum.INDEX_FAILED]: '索引失败',
  [RepoStatusEnum.NOT_GIT_REPO]: '索引失败，请检查当前项目是否为 git 仓库'
};

export class RepoStatusTable {
  static async create(data: Omit<RepoStatus, 'id'>) {
    const db = await SqliteDb.get();
    const result = await db.run(
      `INSERT INTO repo_status (
        git_url, dir_path, branch, commit_id, updated_at, created_at,
        ide_version, plugin_version, platform, total_files, done_files,
        is_building, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        data.git_url,
        data.dir_path,
        data.branch ?? '',
        data.commit_id,
        data.updated_at,
        data.created_at,
        data.ide_version,
        data.plugin_version,
        data.platform,
        data.total_files ?? 0,
        data.done_files ?? 0,
        data.is_building ?? false,
        data.status ?? RepoStatusEnum.SUCCESS
      ]
    );
    return result.lastID;
  }

  static async update(dirPath: string, data: Partial<Omit<RepoStatus, 'id'>>) {
    const db = await SqliteDb.get();
    const updates = Object.entries(data)
      .map(([key]) => `${key} = ?`)
      .join(', ');
    const values = Object.values(data);

    await db.run(`UPDATE repo_status SET ${updates} WHERE dir_path = ?`, [...values, dirPath]);
  }

  static async updateProgress(dirPath: string, totalFiles: number, doneFiles: number, isBuilding: boolean) {
    const db = await SqliteDb.get();
    await db.run(
      `UPDATE repo_status SET total_files = ?, done_files = ?, is_building = ?, updated_at = ? WHERE dir_path = ?`,
      [totalFiles, doneFiles, isBuilding, Date.now(), dirPath]
    );
  }

  static async deleteByDirPath(dirPath: string) {
    const db = await SqliteDb.get();
    await db.run('DELETE FROM repo_status WHERE dir_path = ?', [dirPath]);
  }

  static async findByDirPath(dirPath: string) {
    const db = await SqliteDb.get();
    return await db.get<RepoStatus>('SELECT * FROM repo_status WHERE dir_path = ?', [dirPath]);
  }

  static async findAll() {
    const db = await SqliteDb.get();
    return await db.all<RepoStatus[]>('SELECT * FROM repo_status');
  }

  /**
   * 查询所有仓库状态，按更新时间排序
   * @param order 排序方式，默认为 'ASC'（升序，最新的在后）
   * @returns 按更新时间排序的仓库状态列表
   */
  static async findAllOrderByUpdateTime(order: 'ASC' | 'DESC' = 'ASC') {
    const db = await SqliteDb.get();
    return await db.all<RepoStatus[]>(`SELECT * FROM repo_status ORDER BY updated_at ${order}`);
  }
}
